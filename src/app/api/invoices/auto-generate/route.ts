import { NextResponse } from 'next/server';
import path from 'path';
import { promises as fs } from 'fs';
import { readAllTasks, convertHierarchicalToLegacy } from '../../../../lib/project-tasks/hierarchical-storage';

const INVOICES_PATH = path.join(process.cwd(), 'data/invoices.json');
const PROJECTS_PATH = path.join(process.cwd(), 'data/projects.json');
const EVENTS_LOG_PATH = path.join(process.cwd(), 'data/notifications/notifications-log.json');

interface Task {
  id: number;
  title: string;
  status: string;
  completed: boolean;
  order: number;
  description: string;
}

interface ProjectTask {
  projectId: number;
  title: string;
  organizationId: number;
  tasks: Task[];
}

interface Project {
  projectId: number;
  title: string;
  commissionerId: number;
  freelancerId: number;
  status: string;
  invoicingMethod: 'milestone' | 'completion';
}

export async function POST(request: Request) {
  try {
    const { taskId, projectId, action } = await request.json();

    if (action !== 'task_approved') {
      return NextResponse.json({ message: 'No invoice generation needed' });
    }

    // Load all required data
    const [projectsData, invoicesData, eventsData] = await Promise.all([
      fs.readFile(PROJECTS_PATH, 'utf-8'),
      fs.readFile(INVOICES_PATH, 'utf-8'),
      fs.readFile(EVENTS_LOG_PATH, 'utf-8')
    ]);

    // Read project tasks from hierarchical storage
    const hierarchicalTasks = await readAllTasks();
    const projectTasks: ProjectTask[] = convertHierarchicalToLegacy(hierarchicalTasks);

    const projects: Project[] = JSON.parse(projectsData);
    const invoices = JSON.parse(invoicesData);
    const events = JSON.parse(eventsData);

    // Find the project and task
    const projectTask = projectTasks.find(pt => pt.projectId === projectId);
    const project = projects.find(p => p.projectId === projectId);
    const task = projectTask?.tasks.find(t => t.id === taskId);

    if (!projectTask || !project || !task) {
      return NextResponse.json({ error: 'Project or task not found' }, { status: 404 });
    }

    // Only auto-generate invoices for milestone-based projects
    if (project.invoicingMethod !== 'milestone') {
      return NextResponse.json({
        message: `Project uses ${project.invoicingMethod}-based invoicing. Auto-generation only applies to milestone-based projects.`
      });
    }

    // Check if this task already has an invoice
    const existingInvoice = invoices.find((inv: any) =>
      inv.projectId === projectId &&
      inv.milestones?.some((m: any) => m.taskId === taskId)
    );

    if (existingInvoice) {
      return NextResponse.json({ message: 'Invoice already exists for this task' });
    }

    // Calculate task rate (simplified - could be more sophisticated)
    const baseRate = 1000; // Base rate per task
    const taskRate = baseRate + (task.order * 100); // Increase rate based on task complexity/order

    // Generate invoice number
    const invoiceNumber = `AUTO-${Date.now()}-${projectId}-${taskId}`;

    // Create new invoice entry
    const newInvoice = {
      invoiceNumber,
      freelancerId: project.freelancerId,
      projectId: project.projectId,
      commissionerId: project.commissionerId,
      projectTitle: project.title,
      issueDate: new Date().toISOString().split('T')[0],
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days from now
      totalAmount: taskRate,
      status: 'auto_generated',
      milestones: [
        {
          taskId: task.id,
          description: task.title,
          rate: taskRate,
          approvedAt: new Date().toISOString()
        }
      ],
      isAutoGenerated: true,
      generatedAt: new Date().toISOString()
    };

    // Add to invoices
    invoices.push(newInvoice);
    await fs.writeFile(INVOICES_PATH, JSON.stringify(invoices, null, 2));

    // Create invoice generation event
    const invoiceEvent = {
      id: `evt_${Date.now()}_invoice_auto`,
      timestamp: new Date().toISOString(),
      type: 'invoice_auto_generated',
      notificationType: 42, // New type for auto-generated invoices
      actorId: project.freelancerId,
      targetId: project.commissionerId,
      entityType: 5, // Invoice entity type
      entityId: invoiceNumber,
      metadata: {
        taskTitle: task.title,
        projectTitle: project.title,
        amount: taskRate,
        invoiceNumber
      },
      context: {
        projectId: project.projectId,
        taskId: task.id,
        invoiceId: invoiceNumber
      }
    };

    events.push(invoiceEvent);
    await fs.writeFile(EVENTS_LOG_PATH, JSON.stringify(events, null, 2));

    return NextResponse.json({
      success: true,
      invoice: newInvoice,
      message: `Auto-generated invoice ${invoiceNumber} for approved task: ${task.title}`
    });

  } catch (error) {
    console.error('Error auto-generating invoice:', error);
    return NextResponse.json({ error: 'Failed to auto-generate invoice' }, { status: 500 });
  }
}
