[{"projectId": 301, "taskId": 1, "taskTitle": "Develop colour palette", "notes": [{"date": "2025-06-23", "feedback": "The colours don’t align with the brand’s aesthetic—too muted and off-tone. They lack the contrast and energy we usually lean into. Rework using a palette closer to our existing visuals in the reference files."}]}, {"projectId": 301, "taskId": 2, "taskTitle": "Hero section design", "notes": [{"date": "2025-06-22", "feedback": "This is much better. Well done."}, {"date": "2025-06-18", "feedback": "It seems the last round of feedback wasn’t fully understood—the hero section still dominates the page and the image emphasis hasn’t shifted toward the parks. At this point, we don’t have time for another revision cycle. We’ll be moving forward with an internal adjustment to keep things on track. Thanks for the effort so far."}, {"date": "2025-06-15", "feedback": "The hero section is oversized and pushes key content too far down the page. The image contrast is too harsh, drawing attention away from the rest of the layout. Visually, it centers too much on people when the focus should be on showcasing the parks. This is not what we’re looking for."}]}, {"projectId": 301, "taskId": 3, "taskTitle": "10 year anniversary graphic assets", "notes": [{"date": "2025-06-10", "feedback": "This is awesome, <PERSON><PERSON><PERSON>. Well done!"}]}, {"projectId": 302, "taskId": 4, "taskTitle": "Brand concept iteration", "notes": [{"date": "2025-06-26", "feedback": "Too many contrasting ideas in one board. The client is confused about the identity we’re aiming for. Simplify and prioritize one visual direction in the next round."}, {"date": "2025-06-23", "feedback": "The idea is interesting, but we’re deviating from the brief. Remember the demographic is 16–25 urban creatives, not legacy media professionals."}]}, {"projectId": 302, "taskId": 5, "taskTitle": "Motion graphics intro", "notes": [{"date": "2025-06-20", "feedback": "Feels a bit heavy for social media—consider shorter cuts and fewer type animations to keep it snappy."}]}, {"projectId": 303, "taskId": 6, "taskTitle": "Login & onboarding flow", "notes": [{"date": "2025-05-30", "feedback": "Onboarding flow was tight and intuitive. <PERSON><PERSON> tweaks from QA—great job!"}]}, {"projectId": 303, "taskId": 7, "taskTitle": "iOS testing & deployment", "notes": [{"date": "2025-06-01", "feedback": "App Store build passed with no flags. Impressive work, especially on optimizing load times."}, {"date": "2025-05-28", "feedback": "There was an issue with the crash analytics not syncing. Resolved by build 92A. No further action needed."}]}, {"projectId": 314, "taskId": 202, "taskTitle": "High-fidelity UI design and prototyping", "notes": [{"date": "2025-07-26", "feedback": "THEWREWR INWEOVEWNVO"}]}]