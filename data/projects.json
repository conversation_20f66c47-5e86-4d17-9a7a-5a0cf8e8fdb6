[{"projectId": 301, "title": "Lagos Parks Services website re-design", "description": "Redesigning the public-facing site for Lagos Parks Services to improve user experience, accessibility, and aesthetic appeal.", "organizationId": 1, "typeTags": ["Brand Design", "Content Design"], "commissionerId": 32, "freelancerId": 31, "status": "Ongoing", "dueDate": "2025-08-12", "totalTasks": 5, "invoicingMethod": "milestone"}, {"projectId": 302, "title": "Urbana channel brand refresh", "description": "Brand identity and visual systems for a new African media streaming startup, including pitch decks and tone of voice.", "organizationId": 2, "typeTags": ["Motion Design", "Brand Strategy"], "commissionerId": 33, "freelancerId": 31, "status": "Ongoing", "dueDate": "2025-08-16", "totalTasks": 4, "invoicingMethod": "milestone"}, {"projectId": 303, "title": "Corlax iOS app UX", "description": "UI/UX revamp for a mental wellness tracking app targeting Gen Z and Millennial users.", "organizationId": 3, "typeTags": ["Mobile Design", "UX Research"], "commissionerId": 34, "freelancerId": 31, "status": "Ongoing", "dueDate": "2025-08-30", "totalTasks": 3, "invoicingMethod": "completion", "totalBudget": 9000, "upfrontCommitment": 2000}, {"projectId": 300, "title": "Event Production", "description": "Logistics, talent coordination, and branding for a large-scale tech showcase.", "organizationId": 6, "typeTags": ["Event Management", "Brand Design"], "commissionerId": 37, "freelancerId": 31, "status": "Paused", "dueDate": "2025-08-25", "totalTasks": 53, "invoicingMethod": "milestone"}, {"projectId": 299, "title": "UX Writing", "description": "Crafting microcopy and interaction text for accessibility across multiple product touchpoints.", "organizationId": 7, "typeTags": ["Content Strategy", "UX Writing"], "commissionerId": 38, "freelancerId": 31, "status": "Ongoing", "dueDate": "2025-08-10", "totalTasks": 10, "invoicingMethod": "milestone"}, {"projectId": 304, "title": "Zynate events brand toolkit", "description": "Comprehensive brand toolkit for event marketing including templates, guidelines, and promotional materials.", "organizationId": 4, "typeTags": ["Print Design", "Events"], "commissionerId": 35, "freelancerId": 31, "status": "Ongoing", "dueDate": "2025-08-12", "totalTasks": 3, "invoicingMethod": "completion"}, {"projectId": 305, "title": "HERO research launch collateral", "description": "Research publication materials and launch campaign assets for academic research initiative.", "organizationId": 5, "typeTags": ["Content Strategy", "Editorial Design"], "commissionerId": 36, "freelancerId": 31, "status": "Ongoing", "dueDate": "2025-08-29", "totalTasks": 3, "invoicingMethod": "completion"}, {"projectId": 306, "title": "Nebula CMS landing redesign", "description": "Complete redesign of the CMS platform landing page with focus on user onboarding and conversion optimization.", "organizationId": 3, "typeTags": ["Web Design", "UX Strategy"], "commissionerId": 34, "freelancerId": 31, "status": "Ongoing", "dueDate": "2025-08-05", "totalTasks": 4, "invoicingMethod": "milestone"}, {"projectId": 311, "title": "Lagos Parks Mobile App Development", "description": "Development of a mobile application for Lagos Parks Services to allow citizens to book park facilities and report maintenance issues.", "organizationId": 1, "typeTags": ["Mobile Development", "UI/UX Design"], "commissionerId": 32, "freelancerId": 31, "status": "Completed", "dueDate": "2025-09-15", "totalTasks": 5, "invoicingMethod": "milestone"}, {"projectId": 312, "title": "Lagos Parks Mobile App Development", "description": "Development of a mobile application for Lagos Parks Services to allow citizens to book park facilities and report maintenance issues.", "organizationId": 1, "typeTags": ["Mobile Development", "UI/UX Design"], "commissionerId": 32, "freelancerId": 19, "status": "Completed", "dueDate": "2025-09-15", "totalTasks": 8, "invoicingMethod": "milestone"}, {"projectId": 314, "title": "E-commerce Platform UI Redesign", "description": "Complete redesign of the user interface for an e-commerce platform focusing on conversion optimization and mobile responsiveness.", "organizationId": 1, "typeTags": ["UI/UX Design", "E-commerce"], "commissionerId": 32, "freelancerId": 31, "status": "Ongoing", "dueDate": "2025-09-30", "totalTasks": 2, "invoicingMethod": "completion", "totalBudget": 8000, "upfrontCommitment": 1500}, {"projectId": 315, "title": "Interactive Park Map Web App", "description": "Build an interactive web app for visualizing Lagos park layouts with routes, amenities, and accessibility features.", "organizationId": 1, "typeTags": ["Frontend", "React", "GIS"], "manager": {"name": "<PERSON><PERSON>", "title": "Product Manager", "avatar": "/avatars/neilsan.png", "email": "<EMAIL>"}, "freelancerId": 3, "status": "Active", "dueDate": "2025-08-30", "totalTasks": 1}, {"projectId": 316, "title": "Digital Signage Content Management", "description": "Create a content management system for digital signage across Lagos parks.", "organizationId": 1, "typeTags": ["Digital Signage", "Content Management", "UI Design"], "manager": {"name": "<PERSON><PERSON>", "title": "Product Manager", "avatar": "/avatars/neilsan.png", "email": "<EMAIL>"}, "freelancerId": 2, "status": "Active", "dueDate": "2025-09-08", "totalTasks": 1}]