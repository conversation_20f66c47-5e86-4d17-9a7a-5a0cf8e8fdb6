[{"id": "evt_1752860539720_3", "timestamp": "2025-06-05T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 32, "entityType": 1, "entityId": 2, "metadata": {"taskTitle": "Hero section design", "projectTitle": "Lagos Parks Services website re-design", "link": "https://figma.com/file/hero-section-link", "version": 2}, "context": {"projectId": 301, "taskId": 2}}, {"id": "evt_1752860539720_9", "timestamp": "2025-06-05T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 33, "entityType": 1, "entityId": 5, "metadata": {"taskTitle": "Motion intro animation", "projectTitle": "Urbana channel brand refresh", "link": "https://figma.com/file/motion-intro", "version": 1}, "context": {"projectId": 302, "taskId": 5}}, {"id": "evt_1752860539720_18", "timestamp": "2025-06-05T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 35, "entityType": 1, "entityId": 8, "metadata": {"taskTitle": "Print-ready social media assets", "projectTitle": "Zynate events brand toolkit", "link": "https://figma.com/file/social-assets", "version": 1}, "context": {"projectId": 304, "taskId": 8}}, {"id": "evt_1752860539720_31", "timestamp": "2025-06-05T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 32, "entityType": 1, "entityId": 102, "metadata": {"taskTitle": "Park booking interface mockups", "projectTitle": "Lagos Parks Mobile App Development", "link": "https://figma.com/file/booking-interface", "version": 1}, "context": {"projectId": 311, "taskId": 102}}]