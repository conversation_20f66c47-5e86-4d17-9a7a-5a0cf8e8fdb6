[{"id": "evt_1752860539720_7", "timestamp": "2025-06-04T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 33, "entityType": 1, "entityId": 4, "metadata": {"taskTitle": "Brand concept iteration", "projectTitle": "Urbana channel brand refresh", "link": "https://figma.com/file/brand-concept", "version": 3}, "context": {"projectId": 302, "taskId": 4}}, {"id": "evt_1752860539720_16", "timestamp": "2025-06-04T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 35, "entityType": 1, "entityId": 7, "metadata": {"taskTitle": "Design event templates", "projectTitle": "Zynate events brand toolkit", "link": "https://figma.com/file/event-templates", "version": 2}, "context": {"projectId": 304, "taskId": 7}}, {"id": "evt_1752860539720_20", "timestamp": "2025-06-04T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 36, "entityType": 1, "entityId": 9, "metadata": {"taskTitle": "Write product documentation", "projectTitle": "HERO research launch collateral", "link": "https://docs.google.com/document/product-docs", "version": 2}, "context": {"projectId": 305, "taskId": 9}}, {"id": "evt_1752860539720_22", "timestamp": "2025-06-04T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 34, "entityType": 1, "entityId": 21, "metadata": {"taskTitle": "CTA Button States", "projectTitle": "Nebula CMS landing redesign", "link": "https://figma.com/file/cta-buttons", "version": 2}, "context": {"projectId": 306, "taskId": 21}}, {"id": "evt_1752860539720_29", "timestamp": "2025-06-04T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 32, "entityType": 1, "entityId": 101, "metadata": {"taskTitle": "User authentication flow design", "projectTitle": "Lagos Parks Mobile App Development", "link": "https://figma.com/file/auth-flow-design", "version": 1}, "context": {"projectId": 311, "taskId": 101}}]