[{"id": "evt_1752860539720_5", "timestamp": "2025-06-06T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 32, "entityType": 1, "entityId": 3, "metadata": {"taskTitle": "10 year anniversary graphic assets", "projectTitle": "Lagos Parks Services website re-design", "link": "https://figma.com/file/anniversary-assets", "version": 1}, "context": {"projectId": 301, "taskId": 3}}, {"id": "evt_1752860539720_14", "timestamp": "2025-06-06T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 34, "entityType": 1, "entityId": 16, "metadata": {"taskTitle": "Scroll gestures & microinteractions", "projectTitle": "Corlax iOS app UX", "link": "https://figma.com/file/scroll-gestures", "version": 3}, "context": {"projectId": 303, "taskId": 16}}, {"id": "evt_1752860539720_23", "timestamp": "2025-06-06T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 34, "entityType": 1, "entityId": 23, "metadata": {"taskTitle": "Mobile nav menu prototype", "projectTitle": "Nebula CMS landing redesign", "link": "https://figma.com/file/mobile-nav", "version": 3}, "context": {"projectId": 306, "taskId": 23}}, {"id": "evt_1752860539720_28", "timestamp": "2025-06-06T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 38, "entityType": 1, "entityId": 26, "metadata": {"taskTitle": "Onboarding flow copy", "projectTitle": "UX Writing", "link": "https://docs.google.com/document/onboarding-copy", "version": 1}, "context": {"projectId": 299, "taskId": 26}}, {"id": "evt_1752860539720_32", "timestamp": "2025-06-06T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 32, "entityType": 1, "entityId": 103, "metadata": {"taskTitle": "Maintenance reporting feature", "projectTitle": "Lagos Parks Mobile App Development", "link": "https://figma.com/file/maintenance-reporting", "version": 1}, "context": {"projectId": 311, "taskId": 103}}]