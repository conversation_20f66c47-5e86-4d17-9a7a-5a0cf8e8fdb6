[{"id": "evt_1753735235772_734", "timestamp": "2025-07-28T20:40:35.772Z", "type": "invoice_paid", "notificationType": 41, "actorId": "32", "targetId": 31, "entityType": 3, "entityId": "MF-24BGJ", "metadata": {"invoiceNumber": "MF-24BGJ", "projectTitle": "Lagos Parks Services website re-design", "amount": 5000, "commissionerName": "A commissioner"}, "context": {"projectId": 301, "invoiceNumber": "MF-24BGJ"}}, {"id": "proposal_sent_proposal-1753647229862_1753647229865", "timestamp": "2025-07-27T20:13:49.865Z", "type": "proposal_sent", "notificationType": 80, "entityType": 7, "entityId": "proposal-1753647229862", "metadata": {"proposalTitle": "Lagos Landing Page Rework", "budget": 4000, "timeline": "Not specified", "description": "ibuuhlgnvhdlsogheiro i iownchmlonecelt oinwuclowtulnwotoi ncuwteotuenmtoertuent"}, "context": {"proposalId": "proposal-1753647229862"}}, {"id": "evt_1753647019762_691", "timestamp": "2025-07-27T20:10:19.762Z", "type": "invoice_sent", "notificationType": 40, "actorId": 31, "targetId": 32, "entityType": 3, "entityId": "MF-24BGJ", "metadata": {"invoiceNumber": "MF-24BGJ", "projectTitle": "Lagos Parks Services website re-design", "amount": 5000, "freelancerName": "<PERSON><PERSON><PERSON>"}, "context": {"projectId": 301, "invoiceNumber": "MF-24BGJ"}}, {"id": "proposal_sent_proposal-1753646037613_1753646037617", "timestamp": "2025-07-27T19:53:57.617Z", "type": "proposal_sent", "notificationType": 80, "entityType": 7, "entityId": "proposal-1753646037613", "metadata": {"proposalTitle": "<PERSON><PERSON> is good", "budget": 4000, "timeline": "Not specified", "description": "ibuuhlgnvhdlsogheiro i iownchmlonecelt oinwuclowtulnwotoi ncuwteotuenmtoertuent"}, "context": {"proposalId": "proposal-1753646037613"}}, {"id": "proposal_sent_proposal-1753640473853_1753640473854", "timestamp": "2025-07-27T18:21:13.854Z", "type": "proposal_sent", "notificationType": 80, "entityType": 7, "entityId": "proposal-1753640473853", "metadata": {"proposalTitle": "Love In Lagos", "budget": 4000, "timeline": "Not specified", "description": "biiehnlofvjkofbtljgldbojzvotnibsrooi. oiwunrvoaetonsecugmj9wu vetujwvnmjsceonieutjbnawr0evsihe0 anwo"}, "context": {"proposalId": "proposal-1753640473853"}}, {"id": "proposal_sent_proposal-1753640464349_1753640464355", "timestamp": "2025-07-27T18:21:04.355Z", "type": "proposal_sent", "notificationType": 80, "entityType": 7, "entityId": "proposal-1753640464349", "metadata": {"proposalTitle": "Love In Lagos", "budget": 4000, "timeline": "Not specified", "description": "biiehnlofvjkofbtljgldbojzvotnibsrooi. oiwunrvoaetonsecugmj9wu vetujwvnmjsceonieutjbnawr0evsihe0 anwo"}, "context": {"proposalId": "proposal-1753640464349"}}, {"id": "task_submitted_6_1753537551501", "timestamp": "2025-07-26T13:45:51.501Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 34, "entityType": 1, "entityId": 6, "metadata": {"taskTitle": "Login & onboarding flow", "projectTitle": "Corlax iOS app UX", "version": 1, "action": "submit"}, "context": {"projectId": 303, "taskId": 6}}, {"id": "evt_1752860539720_100", "timestamp": "2025-07-18T00:00:00.000Z", "type": "product_purchased", "notificationType": 50, "actorId": 1, "targetId": 32, "entityType": 6, "entityId": "#102001", "metadata": {"productTitle": "Project Management Templates", "amount": 25, "buyerName": "<PERSON>"}, "context": {"productId": "#102001"}}, {"id": "evt_1752860539720_101", "timestamp": "2025-07-17T00:00:00.000Z", "type": "product_purchased", "notificationType": 50, "actorId": 3, "targetId": 32, "entityType": 6, "entityId": "#102002", "metadata": {"productTitle": "Freelancer Onboarding Guide", "amount": 35, "buyerName": "<PERSON>"}, "context": {"productId": "#102002"}}, {"id": "evt_1752860539720_102", "timestamp": "2025-07-16T00:00:00.000Z", "type": "product_purchased", "notificationType": 50, "actorId": 5, "targetId": 32, "entityType": 6, "entityId": "#102001", "metadata": {"productTitle": "Project Management Templates", "amount": 25, "buyerName": "<PERSON>"}, "context": {"productId": "#102001"}}, {"id": "evt_1752860539720_103", "timestamp": "2025-07-15T00:00:00.000Z", "type": "product_purchased", "notificationType": 50, "actorId": 8, "targetId": 32, "entityType": 6, "entityId": "#102001", "metadata": {"productTitle": "Project Management Templates", "amount": 25, "buyerName": "<PERSON>"}, "context": {"productId": "#102001"}}, {"id": "evt_1752860539720_104", "timestamp": "2025-07-14T00:00:00.000Z", "type": "product_purchased", "notificationType": 50, "actorId": 10, "targetId": 32, "entityType": 6, "entityId": "#102001", "metadata": {"productTitle": "Project Management Templates", "amount": 25, "buyerName": "<PERSON>"}, "context": {"productId": "#102001"}}, {"id": "evt_1752860539720_41", "timestamp": "2025-07-13T00:00:00.000Z", "type": "invoice_paid", "notificationType": 41, "actorId": 32, "targetId": 31, "entityType": 5, "entityId": "MGL100000-M1", "metadata": {"invoiceNumber": "MGL100000-M1", "projectTitle": "Lagos Parks Services website re-design", "taskTitle": "Develop colour palette", "amount": 1748, "description": "Develop colour palette"}, "context": {"projectId": 301, "invoiceId": "MGL100000-M1"}}, {"id": "evt_1752860539720_43", "timestamp": "2025-07-13T00:00:00.000Z", "type": "invoice_paid", "notificationType": 41, "actorId": 33, "targetId": 31, "entityType": 5, "entityId": "CUSTOM-001-M1", "metadata": {"invoiceNumber": "CUSTOM-001-M1", "projectTitle": "Custom Brand Consultation", "taskTitle": "Brand strategy consultation", "amount": 1200, "description": "Brand strategy consultation"}, "context": {"projectId": null, "invoiceId": "CUSTOM-001-M1"}}, {"id": "evt_1752860539720_45", "timestamp": "2025-07-13T00:00:00.000Z", "type": "invoice_paid", "notificationType": 41, "actorId": 34, "targetId": 31, "entityType": 5, "entityId": "INV-303-001", "metadata": {"invoiceNumber": "MGL303001", "projectTitle": "Corlax iOS app UX", "taskTitle": "Login & onboarding flow", "amount": 2500, "description": "Login & onboarding flow"}, "context": {"projectId": 303, "invoiceId": "INV-303-001"}}, {"id": "evt_1752860539720_47", "timestamp": "2025-07-13T00:00:00.000Z", "type": "invoice_paid", "notificationType": 41, "actorId": 38, "targetId": 31, "entityType": 5, "entityId": "INV-299-001", "metadata": {"invoiceNumber": "MGL299001", "projectTitle": "UX Writing", "taskTitle": "Accessibility microcopy audit", "amount": 2500, "description": "Accessibility microcopy audit"}, "context": {"projectId": 299, "invoiceId": "INV-299-001"}}, {"id": "evt_1752860539720_49", "timestamp": "2025-07-13T00:00:00.000Z", "type": "invoice_paid", "notificationType": 41, "actorId": 35, "targetId": 31, "entityType": 5, "entityId": "INV-304-001", "metadata": {"invoiceNumber": "MGL304001", "projectTitle": "Zynate events brand toolkit", "taskTitle": "Design event templates", "amount": 2500, "description": "Design event templates"}, "context": {"projectId": 304, "invoiceId": "INV-304-001"}}, {"id": "evt_1752860539720_51", "timestamp": "2025-07-13T00:00:00.000Z", "type": "invoice_paid", "notificationType": 41, "actorId": 36, "targetId": 31, "entityType": 5, "entityId": "INV-305-001", "metadata": {"invoiceNumber": "MGL305001", "projectTitle": "HERO research launch collateral", "taskTitle": "Write product documentation", "amount": 2500, "description": "Write product documentation"}, "context": {"projectId": 305, "invoiceId": "INV-305-001"}}, {"id": "evt_1752860539720_53", "timestamp": "2025-07-13T00:00:00.000Z", "type": "invoice_paid", "notificationType": 41, "actorId": 34, "targetId": 31, "entityType": 5, "entityId": "INV-306-001", "metadata": {"invoiceNumber": "MGL306001", "projectTitle": "Nebula CMS landing redesign", "taskTitle": "CTA Button States", "amount": 2500, "description": "CTA Button States"}, "context": {"projectId": 306, "invoiceId": "INV-306-001"}}, {"id": "evt_1752860539720_55", "timestamp": "2025-07-13T00:00:00.000Z", "type": "invoice_paid", "notificationType": 41, "actorId": 32, "targetId": 31, "entityType": 5, "entityId": "INV-311-001", "metadata": {"invoiceNumber": "MGL311001", "projectTitle": "Lagos Parks Mobile App Development", "taskTitle": "User authentication flow design", "amount": 2500, "description": "User authentication flow design"}, "context": {"projectId": 311, "invoiceId": "INV-311-001"}}, {"id": "evt_1752860539720_105", "timestamp": "2025-07-13T00:00:00.000Z", "type": "product_purchased", "notificationType": 50, "actorId": 12, "targetId": 32, "entityType": 6, "entityId": "#102002", "metadata": {"productTitle": "Freelancer Onboarding Guide", "amount": 35, "buyerName": "<PERSON>"}, "context": {"productId": "#102002"}}, {"id": "evt_1752860539720_106", "timestamp": "2025-07-12T00:00:00.000Z", "type": "product_purchased", "notificationType": 50, "actorId": 15, "targetId": 32, "entityType": 6, "entityId": "#102001", "metadata": {"productTitle": "Project Management Templates", "amount": 25, "buyerName": "<PERSON>"}, "context": {"productId": "#102001"}}, {"id": "evt_1752860539720_gig_2", "timestamp": "2025-07-10T16:45:00Z", "type": "gig_request_sent", "notificationType": 61, "actorId": 33, "targetId": 31, "entityType": 3, "entityId": 208, "metadata": {"gigTitle": "Brand Voice Guidelines", "organizationName": "Urbana Documentary Channel", "budgetMin": 1500, "budgetMax": 2500}, "context": {"gigId": 108, "organizationId": 2, "requestId": 208}}, {"id": "evt_1752860539720_40", "timestamp": "2025-07-10T00:00:00.000Z", "type": "invoice_sent", "notificationType": 40, "actorId": 31, "targetId": 32, "entityType": 5, "entityId": "MGL100000-M1", "metadata": {"invoiceNumber": "MGL100000-M1", "projectTitle": "Lagos Parks Services website re-design", "amount": 1748, "description": "Milestone 1 completion"}, "context": {"projectId": 301, "invoiceId": "MGL100000-M1"}}, {"id": "evt_1752860539720_42", "timestamp": "2025-07-10T00:00:00.000Z", "type": "invoice_sent", "notificationType": 40, "actorId": 31, "targetId": 33, "entityType": 5, "entityId": "CUSTOM-001-M1", "metadata": {"invoiceNumber": "CUSTOM-001-M1", "projectTitle": "Custom Brand Consultation", "amount": 1200, "description": "Brand strategy consultation"}, "context": {"projectId": null, "invoiceId": "CUSTOM-001-M1"}}, {"id": "evt_1752860539720_44", "timestamp": "2025-07-10T00:00:00.000Z", "type": "invoice_sent", "notificationType": 40, "actorId": 31, "targetId": 34, "entityType": 5, "entityId": "INV-303-001", "metadata": {"invoiceNumber": "MGL303001", "projectTitle": "Corlax iOS app UX", "amount": 2500, "description": "Milestone 1 completion"}, "context": {"projectId": 303, "invoiceId": "INV-303-001"}}, {"id": "evt_1752860539720_46", "timestamp": "2025-07-10T00:00:00.000Z", "type": "invoice_sent", "notificationType": 40, "actorId": 31, "targetId": 38, "entityType": 5, "entityId": "INV-299-001", "metadata": {"invoiceNumber": "MGL299001", "projectTitle": "UX Writing", "amount": 2500, "description": "Milestone 1 completion"}, "context": {"projectId": 299, "invoiceId": "INV-299-001"}}, {"id": "evt_1752860539720_48", "timestamp": "2025-07-10T00:00:00.000Z", "type": "invoice_sent", "notificationType": 40, "actorId": 31, "targetId": 35, "entityType": 5, "entityId": "INV-304-001", "metadata": {"invoiceNumber": "MGL304001", "projectTitle": "Zynate events brand toolkit", "amount": 2500, "description": "Milestone 1 completion"}, "context": {"projectId": 304, "invoiceId": "INV-304-001"}}, {"id": "evt_1752860539720_50", "timestamp": "2025-07-10T00:00:00.000Z", "type": "invoice_sent", "notificationType": 40, "actorId": 31, "targetId": 36, "entityType": 5, "entityId": "INV-305-001", "metadata": {"invoiceNumber": "MGL305001", "projectTitle": "HERO research launch collateral", "amount": 2500, "description": "Milestone 1 completion"}, "context": {"projectId": 305, "invoiceId": "INV-305-001"}}, {"id": "evt_1752860539720_52", "timestamp": "2025-07-10T00:00:00.000Z", "type": "invoice_sent", "notificationType": 40, "actorId": 31, "targetId": 34, "entityType": 5, "entityId": "INV-306-001", "metadata": {"invoiceNumber": "MGL306001", "projectTitle": "Nebula CMS landing redesign", "amount": 2500, "description": "Milestone 1 completion"}, "context": {"projectId": 306, "invoiceId": "INV-306-001"}}, {"id": "evt_1752860539720_54", "timestamp": "2025-07-10T00:00:00.000Z", "type": "invoice_sent", "notificationType": 40, "actorId": 31, "targetId": 32, "entityType": 5, "entityId": "INV-311-001", "metadata": {"invoiceNumber": "MGL311001", "projectTitle": "Lagos Parks Mobile App Development", "amount": 2500, "description": "Milestone 1 completion"}, "context": {"projectId": 311, "invoiceId": "INV-311-001"}}, {"id": "evt_1752860539720_107", "timestamp": "2025-07-10T00:00:00.000Z", "type": "product_purchased", "notificationType": 50, "actorId": 18, "targetId": 32, "entityType": 6, "entityId": "#102002", "metadata": {"productTitle": "Freelancer Onboarding Guide", "amount": 35, "buyerName": "<PERSON>"}, "context": {"productId": "#102002"}}, {"id": "evt_1752860539720_gig_1", "timestamp": "2025-07-08T09:15:00Z", "type": "gig_request_sent", "notificationType": 61, "actorId": 32, "targetId": 31, "entityType": 3, "entityId": 207, "metadata": {"gigTitle": "Content Strategy for Parks Website", "organizationName": "Lagos Parks Services", "budgetMin": 2000, "budgetMax": 3500}, "context": {"gigId": 107, "organizationId": 1, "requestId": 207}}, {"id": "evt_1752860539720_108", "timestamp": "2025-07-08T00:00:00.000Z", "type": "product_purchased", "notificationType": 50, "actorId": 20, "targetId": 32, "entityType": 6, "entityId": "#102001", "metadata": {"productTitle": "Project Management Templates", "amount": 25, "buyerName": "<PERSON>"}, "context": {"productId": "#102001"}}, {"id": "evt_1752860539720_109", "timestamp": "2025-07-05T00:00:00.000Z", "type": "product_purchased", "notificationType": 50, "actorId": 22, "targetId": 32, "entityType": 6, "entityId": "#102002", "metadata": {"productTitle": "Freelancer Onboarding Guide", "amount": 35, "buyerName": "<PERSON>"}, "context": {"productId": "#102002"}}, {"id": "evt_1752860539720_product_5", "timestamp": "2025-07-04T11:30:00.000Z", "type": "product_purchased", "notificationType": 100, "actorId": 995, "targetId": 31, "entityType": 6, "entityId": "#101002", "metadata": {"productTitle": "Content Design 101", "amount": 25, "category": "Digital Course"}, "context": {"productId": "#101002", "purchaseId": "purchase_005"}}, {"id": "evt_1752860539720_product_4", "timestamp": "2025-07-03T16:20:00.000Z", "type": "product_purchased", "notificationType": 100, "actorId": 996, "targetId": 31, "entityType": 6, "entityId": "#101001", "metadata": {"productTitle": "Digital Marketing Playbook", "amount": 15, "category": "Digital Course"}, "context": {"productId": "#101001", "purchaseId": "purchase_004"}}, {"id": "evt_1752860539720_110", "timestamp": "2025-07-03T00:00:00.000Z", "type": "product_purchased", "notificationType": 50, "actorId": 25, "targetId": 32, "entityType": 6, "entityId": "#102001", "metadata": {"productTitle": "Project Management Templates", "amount": 25, "buyerName": "<PERSON>"}, "context": {"productId": "#102001"}}, {"id": "evt_1752860539720_product_3", "timestamp": "2025-07-02T09:45:00.000Z", "type": "product_purchased", "notificationType": 100, "actorId": 997, "targetId": 31, "entityType": 6, "entityId": "#101001", "metadata": {"productTitle": "Digital Marketing Playbook", "amount": 15, "category": "Digital Course"}, "context": {"productId": "#101001", "purchaseId": "purchase_003"}}, {"id": "evt_1752860539720_product_2", "timestamp": "2025-07-01T14:15:00.000Z", "type": "product_purchased", "notificationType": 100, "actorId": 998, "targetId": 31, "entityType": 6, "entityId": "#101002", "metadata": {"productTitle": "Content Design 101", "amount": 25, "category": "Digital Course"}, "context": {"productId": "#101002", "purchaseId": "purchase_002"}}, {"id": "evt_1752860539720_product_1", "timestamp": "2025-07-01T10:30:00.000Z", "type": "product_purchased", "notificationType": 100, "actorId": 999, "targetId": 31, "entityType": 6, "entityId": "#101001", "metadata": {"productTitle": "Digital Marketing Playbook", "amount": 15, "category": "Digital Course"}, "context": {"productId": "#101001", "purchaseId": "purchase_001"}}]