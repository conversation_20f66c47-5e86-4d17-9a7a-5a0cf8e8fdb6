[{"id": "evt_1752860539720_111", "timestamp": "2025-07-01T00:00:00.000Z", "type": "product_purchased", "notificationType": 50, "actorId": 27, "targetId": 32, "entityType": 6, "entityId": "#102002", "metadata": {"productTitle": "Freelancer Onboarding Guide", "amount": 35, "buyerName": "<PERSON>"}, "context": {"productId": "#102002"}}, {"id": "evt_1752860539720_37", "timestamp": "2025-06-18T00:00:00.000Z", "type": "gig_applied", "notificationType": 60, "actorId": 3, "targetId": 32, "entityType": 3, "entityId": 5, "metadata": {"gigTitle": "Interactive Park Map Web App", "applicationMessage": "Application submitted"}, "context": {"gigId": 5, "applicationId": 3}}, {"id": "evt_1752860539720_36", "timestamp": "2025-06-17T00:00:00.000Z", "type": "gig_applied", "notificationType": 60, "actorId": 1, "targetId": 32, "entityType": 3, "entityId": 5, "metadata": {"gigTitle": "Interactive Park Map Web App", "applicationMessage": "Application submitted"}, "context": {"gigId": 5, "applicationId": 2}}, {"id": "evt_1752860539720_35", "timestamp": "2025-06-16T00:00:00.000Z", "type": "gig_applied", "notificationType": 60, "actorId": 29, "targetId": 32, "entityType": 3, "entityId": 5, "metadata": {"gigTitle": "Interactive Park Map Web App", "applicationMessage": "Application submitted"}, "context": {"gigId": 5, "applicationId": 1}}, {"id": "evt_1752860539720_38", "timestamp": "2025-06-16T00:00:00.000Z", "type": "gig_applied", "notificationType": 60, "actorId": 11, "targetId": 32, "entityType": 3, "entityId": 6, "metadata": {"gigTitle": "Park Visitor Mobile App", "applicationMessage": "Application submitted"}, "context": {"gigId": 6, "applicationId": 4}}, {"id": "evt_1752860539720_39", "timestamp": "2025-06-16T00:00:00.000Z", "type": "gig_applied", "notificationType": 60, "actorId": 2, "targetId": 32, "entityType": 3, "entityId": 7, "metadata": {"gigTitle": "Digital Signage Content Management", "applicationMessage": "Application submitted"}, "context": {"gigId": 7, "applicationId": 5}}, {"id": "evt_1752860539720_26", "timestamp": "2025-06-12T00:00:00.000Z", "type": "task_rejected", "notificationType": 3, "actorId": 34, "targetId": 31, "entityType": 1, "entityId": 28, "metadata": {"taskTitle": "URGENT: <PERSON><PERSON> redesign", "projectTitle": "Nebula CMS landing redesign", "feedback": "Please revise the design to better align with brand guidelines"}, "context": {"projectId": 306, "taskId": 28}}, {"id": "evt_1752860539720_27", "timestamp": "2025-06-12T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 34, "entityType": 1, "entityId": 29, "metadata": {"taskTitle": "URGENT: Footer contact form", "projectTitle": "Nebula CMS landing redesign", "link": "https://figma.com/file/footer-form", "version": 3}, "context": {"projectId": 306, "taskId": 29}}, {"id": "evt_1752860539720_25", "timestamp": "2025-06-11T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 34, "entityType": 1, "entityId": 28, "metadata": {"taskTitle": "URGENT: <PERSON><PERSON> redesign", "projectTitle": "Nebula CMS landing redesign", "link": "https://figma.com/file/header-redesign", "version": 4}, "context": {"projectId": 306, "taskId": 28}}, {"id": "evt_1752860539719_2", "timestamp": "2025-06-09T02:00:00.000Z", "type": "project_pause_accepted", "notificationType": 23, "actorId": 37, "targetId": 31, "entityType": 2, "entityId": 300, "metadata": {"projectTitle": "Event Production"}, "context": {"projectId": 300}}, {"id": "evt_1752860539719_1", "timestamp": "2025-06-09T00:00:00.000Z", "type": "project_pause_requested", "notificationType": 22, "actorId": 31, "targetId": 37, "entityType": 2, "entityId": 300, "metadata": {"projectTitle": "Event Production", "pauseReason": "Need to address some technical challenges before proceeding"}, "context": {"projectId": 300}}, {"id": "evt_1752860539720_13", "timestamp": "2025-06-09T00:00:00.000Z", "type": "task_approved", "notificationType": 2, "actorId": 33, "targetId": 31, "entityType": 1, "entityId": 13, "metadata": {"taskTitle": "Client review deck", "projectTitle": "Urbana channel brand refresh"}, "context": {"projectId": 302, "taskId": 13}}, {"id": "evt_1752860539720_11", "timestamp": "2025-06-08T00:00:00.000Z", "type": "task_approved", "notificationType": 2, "actorId": 33, "targetId": 31, "entityType": 1, "entityId": 15, "metadata": {"taskTitle": "Finalize brand icons", "projectTitle": "Urbana channel brand refresh"}, "context": {"projectId": 302, "taskId": 15}}, {"id": "evt_1752860539720_12", "timestamp": "2025-06-08T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 33, "entityType": 1, "entityId": 13, "metadata": {"taskTitle": "Client review deck", "projectTitle": "Urbana channel brand refresh", "link": "https://figma.com/file/client-deck", "version": 2}, "context": {"projectId": 302, "taskId": 13}}, {"id": "evt_1752860539720_34", "timestamp": "2025-06-08T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 32, "entityType": 1, "entityId": 105, "metadata": {"taskTitle": "Push notification system design", "projectTitle": "Lagos Parks Mobile App Development", "link": "https://figma.com/file/notification-system", "version": 1}, "context": {"projectId": 311, "taskId": 105}}, {"id": "evt_1752860539720_6", "timestamp": "2025-06-07T00:00:00.000Z", "type": "task_approved", "notificationType": 2, "actorId": 32, "targetId": 31, "entityType": 1, "entityId": 3, "metadata": {"taskTitle": "10 year anniversary graphic assets", "projectTitle": "Lagos Parks Services website re-design"}, "context": {"projectId": 301, "taskId": 3}}, {"id": "evt_1752860539720_10", "timestamp": "2025-06-07T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 33, "entityType": 1, "entityId": 15, "metadata": {"taskTitle": "Finalize brand icons", "projectTitle": "Urbana channel brand refresh", "link": "https://figma.com/file/brand-icons", "version": 2}, "context": {"projectId": 302, "taskId": 15}}, {"id": "evt_1752860539720_15", "timestamp": "2025-06-07T00:00:00.000Z", "type": "task_rejected", "notificationType": 3, "actorId": 34, "targetId": 31, "entityType": 1, "entityId": 16, "metadata": {"taskTitle": "Scroll gestures & microinteractions", "projectTitle": "Corlax iOS app UX", "feedback": "Please revise the design to better align with brand guidelines"}, "context": {"projectId": 303, "taskId": 16}}, {"id": "evt_1752860539720_24", "timestamp": "2025-06-07T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 34, "entityType": 1, "entityId": 24, "metadata": {"taskTitle": "Carousel animation mock", "projectTitle": "Nebula CMS landing redesign", "link": "https://figma.com/file/carousel-animation", "version": 2}, "context": {"projectId": 306, "taskId": 24}}, {"id": "evt_1752860539720_33", "timestamp": "2025-06-07T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 32, "entityType": 1, "entityId": 104, "metadata": {"taskTitle": "Navigation and menu structure", "projectTitle": "Lagos Parks Mobile App Development", "link": "https://figma.com/file/navigation-menu", "version": 1}, "context": {"projectId": 311, "taskId": 104}}, {"id": "evt_1752860539720_4", "timestamp": "2025-06-06T00:00:00.000Z", "type": "task_rejected", "notificationType": 3, "actorId": 32, "targetId": 31, "entityType": 1, "entityId": 2, "metadata": {"taskTitle": "Hero section design", "projectTitle": "Lagos Parks Services website re-design", "feedback": "Please revise the design to better align with brand guidelines"}, "context": {"projectId": 301, "taskId": 2}}, {"id": "evt_1752860539720_5", "timestamp": "2025-06-06T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 32, "entityType": 1, "entityId": 3, "metadata": {"taskTitle": "10 year anniversary graphic assets", "projectTitle": "Lagos Parks Services website re-design", "link": "https://figma.com/file/anniversary-assets", "version": 1}, "context": {"projectId": 301, "taskId": 3}}, {"id": "evt_1752860539720_14", "timestamp": "2025-06-06T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 34, "entityType": 1, "entityId": 16, "metadata": {"taskTitle": "Scroll gestures & microinteractions", "projectTitle": "Corlax iOS app UX", "link": "https://figma.com/file/scroll-gestures", "version": 3}, "context": {"projectId": 303, "taskId": 16}}, {"id": "evt_1752860539720_19", "timestamp": "2025-06-06T00:00:00.000Z", "type": "task_approved", "notificationType": 2, "actorId": 35, "targetId": 31, "entityType": 1, "entityId": 8, "metadata": {"taskTitle": "Print-ready social media assets", "projectTitle": "Zynate events brand toolkit"}, "context": {"projectId": 304, "taskId": 8}}, {"id": "evt_1752860539720_23", "timestamp": "2025-06-06T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 34, "entityType": 1, "entityId": 23, "metadata": {"taskTitle": "Mobile nav menu prototype", "projectTitle": "Nebula CMS landing redesign", "link": "https://figma.com/file/mobile-nav", "version": 3}, "context": {"projectId": 306, "taskId": 23}}, {"id": "evt_1752860539720_28", "timestamp": "2025-06-06T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 38, "entityType": 1, "entityId": 26, "metadata": {"taskTitle": "Onboarding flow copy", "projectTitle": "UX Writing", "link": "https://docs.google.com/document/onboarding-copy", "version": 1}, "context": {"projectId": 299, "taskId": 26}}, {"id": "evt_1752860539720_32", "timestamp": "2025-06-06T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 32, "entityType": 1, "entityId": 103, "metadata": {"taskTitle": "Maintenance reporting feature", "projectTitle": "Lagos Parks Mobile App Development", "link": "https://figma.com/file/maintenance-reporting", "version": 1}, "context": {"projectId": 311, "taskId": 103}}, {"id": "evt_1752860539720_3", "timestamp": "2025-06-05T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 32, "entityType": 1, "entityId": 2, "metadata": {"taskTitle": "Hero section design", "projectTitle": "Lagos Parks Services website re-design", "link": "https://figma.com/file/hero-section-link", "version": 2}, "context": {"projectId": 301, "taskId": 2}}, {"id": "evt_1752860539720_8", "timestamp": "2025-06-05T00:00:00.000Z", "type": "task_rejected", "notificationType": 3, "actorId": 33, "targetId": 31, "entityType": 1, "entityId": 4, "metadata": {"taskTitle": "Brand concept iteration", "projectTitle": "Urbana channel brand refresh", "feedback": "Please revise the design to better align with brand guidelines"}, "context": {"projectId": 302, "taskId": 4}}, {"id": "evt_1752860539720_9", "timestamp": "2025-06-05T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 33, "entityType": 1, "entityId": 5, "metadata": {"taskTitle": "Motion intro animation", "projectTitle": "Urbana channel brand refresh", "link": "https://figma.com/file/motion-intro", "version": 1}, "context": {"projectId": 302, "taskId": 5}}, {"id": "evt_1752860539720_17", "timestamp": "2025-06-05T00:00:00.000Z", "type": "task_rejected", "notificationType": 3, "actorId": 35, "targetId": 31, "entityType": 1, "entityId": 7, "metadata": {"taskTitle": "Design event templates", "projectTitle": "Zynate events brand toolkit", "feedback": "Please revise the design to better align with brand guidelines"}, "context": {"projectId": 304, "taskId": 7}}, {"id": "evt_1752860539720_18", "timestamp": "2025-06-05T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 35, "entityType": 1, "entityId": 8, "metadata": {"taskTitle": "Print-ready social media assets", "projectTitle": "Zynate events brand toolkit", "link": "https://figma.com/file/social-assets", "version": 1}, "context": {"projectId": 304, "taskId": 8}}, {"id": "evt_1752860539720_21", "timestamp": "2025-06-05T00:00:00.000Z", "type": "task_rejected", "notificationType": 3, "actorId": 36, "targetId": 31, "entityType": 1, "entityId": 9, "metadata": {"taskTitle": "Write product documentation", "projectTitle": "HERO research launch collateral", "feedback": "Please revise the design to better align with brand guidelines"}, "context": {"projectId": 305, "taskId": 9}}, {"id": "evt_1752860539720_30", "timestamp": "2025-06-05T00:00:00.000Z", "type": "task_approved", "notificationType": 2, "actorId": 32, "targetId": 31, "entityType": 1, "entityId": 101, "metadata": {"taskTitle": "User authentication flow design", "projectTitle": "Lagos Parks Mobile App Development"}, "context": {"projectId": 311, "taskId": 101}}, {"id": "evt_1752860539720_31", "timestamp": "2025-06-05T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 32, "entityType": 1, "entityId": 102, "metadata": {"taskTitle": "Park booking interface mockups", "projectTitle": "Lagos Parks Mobile App Development", "link": "https://figma.com/file/booking-interface", "version": 1}, "context": {"projectId": 311, "taskId": 102}}, {"id": "evt_1752860539720_7", "timestamp": "2025-06-04T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 33, "entityType": 1, "entityId": 4, "metadata": {"taskTitle": "Brand concept iteration", "projectTitle": "Urbana channel brand refresh", "link": "https://figma.com/file/brand-concept", "version": 3}, "context": {"projectId": 302, "taskId": 4}}, {"id": "evt_1752860539720_16", "timestamp": "2025-06-04T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 35, "entityType": 1, "entityId": 7, "metadata": {"taskTitle": "Design event templates", "projectTitle": "Zynate events brand toolkit", "link": "https://figma.com/file/event-templates", "version": 2}, "context": {"projectId": 304, "taskId": 7}}, {"id": "evt_1752860539720_20", "timestamp": "2025-06-04T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 36, "entityType": 1, "entityId": 9, "metadata": {"taskTitle": "Write product documentation", "projectTitle": "HERO research launch collateral", "link": "https://docs.google.com/document/product-docs", "version": 2}, "context": {"projectId": 305, "taskId": 9}}, {"id": "evt_1752860539720_22", "timestamp": "2025-06-04T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 34, "entityType": 1, "entityId": 21, "metadata": {"taskTitle": "CTA Button States", "projectTitle": "Nebula CMS landing redesign", "link": "https://figma.com/file/cta-buttons", "version": 2}, "context": {"projectId": 306, "taskId": 21}}, {"id": "evt_1752860539720_29", "timestamp": "2025-06-04T00:00:00.000Z", "type": "task_submitted", "notificationType": 1, "actorId": 31, "targetId": 32, "entityType": 1, "entityId": 101, "metadata": {"taskTitle": "User authentication flow design", "projectTitle": "Lagos Parks Mobile App Development", "link": "https://figma.com/file/auth-flow-design", "version": 1}, "context": {"projectId": 311, "taskId": 101}}]